import { YTAlertGlobal } from '@bookln/cross-platform-components';
import { AlertTriangle } from '@bookln/icon-lucide';
import { PermissionComponents } from '@bookln/permission';
import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import {
  FloatingBallProvider,
  useFloatingAudioBall,
} from '@jgl/biz-components';
import { agreementStateAtom, store, tamaguiThemeNameAtom } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { JglThemeProvider } from '@jgl/ui-v4';
import {
  DefaultTheme,
  ThemeProvider as NavigationThemeProvider,
} from '@react-navigation/native';
import { captureEvent, wrap } from '@sentry/react-native';
import { QueryProvider } from '@yunti-private/net-query-hooks';
import { MemoryLogger } from '@yunti-private/rn-memory-logger';
import type { ErrorBoundaryProps } from 'expo-router';
import { Stack } from 'expo-router';
import { useAtom, useAtomValue } from 'jotai';
import { Suspense, useCallback, useEffect, useMemo } from 'react';
import { Platform, Pressable, Text, View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { RootSiblingParent } from 'react-native-root-siblings';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import { Toaster } from 'sonner';
import { FontLanguage, TamaguiProvider, Theme } from 'tamagui';
import { atomMap } from '../atom';
import { AiBookBottomToolbar } from '../components/aiBook/common/AiBookBottomToolbar';
import { AuthGuideModal } from '../components/AuthGuideModal';
import { CustomBackButton } from '../components/CustomBackButton';
import { LearnToolsModal } from '../components/LearnToolsModal';
import { SimpleSuspenseFallback } from '../components/SimpleSuspenseFallback';
import { UpgradeModal } from '../components/UpgradeModal';
import { useAiBookHandleIntentCommand } from '../hooks/aiBook/useAiBookHandleIntentCommand';
import { useInitAfterRender } from '../hooks/useInitAfterRender';
import { useInitBeforeRender } from '../hooks/useInitBeforeRender';
import { useUmengPageStats } from '../hooks/useUmengPageStats';
import { initBeforeLaunch } from '../init/initApp';
import { config as tamaguiConfig } from '../tamagui.config';

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: 'index',
};

// 在渲染UI之前的一些初始化工作
initBeforeLaunch();

export function RootLayout() {
  // 在渲染UI之前的一些初始化工作
  useInitBeforeRender();

  const isNetContainerInitialized = useAtomValue(
    atomMap.isNetContainerInitializedAtom,
  );

  if (isNetContainerInitialized) {
    // 开屏页已经隐藏，渲染 UI
    return (
      <GestureHandlerRootView style={{ flex: 1, backgroundColor: 'white' }}>
        <KeyboardProvider statusBarTranslucent navigationBarTranslucent>
          <SafeAreaProvider>
            <Suspense
              // agreementStateAtom 使用了异步 atom
              // 需要启用 Suspense ，正好尝试一下
              fallback={<SimpleSuspenseFallback />}
            >
              <Provider store={store}>
                <QueryProvider networking={container.net()}>
                  <JglThemeProvider initialTheme='light'>
                    <RootLayoutNav />
                  </JglThemeProvider>
                </QueryProvider>
              </Provider>
            </Suspense>
          </SafeAreaProvider>
        </KeyboardProvider>
      </GestureHandlerRootView>
    );
  }

  return null;
}

function RootLayoutNav() {
  const tamaguiThemeName = useAtomValue(tamaguiThemeNameAtom);

  // 在渲染UI之后的一些初始化工作
  useInitAfterRender();

  useFloatingAudioBall();

  return (
    <RootSiblingParent>
      <TamaguiProvider config={tamaguiConfig}>
        <FontLanguage body='default'>
          <ActionSheetProvider>
            <BottomSheetModalProvider>
              <Theme name={tamaguiThemeName}>
                <FloatingBallProvider>
                  <Children />
                </FloatingBallProvider>
              </Theme>
            </BottomSheetModalProvider>
          </ActionSheetProvider>
        </FontLanguage>
      </TamaguiProvider>
    </RootSiblingParent>
  );
}

const Children = () => {
  // const navigationTheme = useNavigationTheme();
  const [agreementState] = useAtom(agreementStateAtom);
  const aiBookInfo = useAtomValue(atomMap.aiBookInfo);
  const [learnToolsModalVisible, setLearnToolsModalVisible] = useAtom(
    atomMap.learnToolsModalVisibleAtom,
  );
  const renderInputToolBarTopNode = useCallback(() => {
    return <AiBookBottomToolbar paddingHorizontal={8} />;
  }, []);
  const { handleAiBookIntentCommand } = useAiBookHandleIntentCommand();

  // 启用友盟页面统计
  useUmengPageStats();

  const navigationOptions = useMemo(() => {
    return {
      navigationBarTranslucent: true,
      navigationBarColor: '#00000000',

      // 统一设置背景色，避免透明
      contentStyle: { backgroundColor: '$color1' },
    };
  }, []);

  /**
   * 学习工具模态框消失
   */
  const onLearnToolsModalDismiss = useCallback(() => {
    setLearnToolsModalVisible(false);
  }, [setLearnToolsModalVisible]);

  return (
    <NavigationThemeProvider value={DefaultTheme}>
      {Platform.OS === 'web' && (
        <Toaster

        // TODO: aini 20250709 - 测试一下
        />
      )}
      <AuthGuideModal />
      <UpgradeModal />
      <PermissionComponents.PermissionDescView />
      <YTAlertGlobal />
      <Stack
        screenOptions={{
          headerTitleAlign: 'center',
          headerLeft: () => {
            return <CustomBackButton />;
          },
          animation: Platform.OS === 'ios' ? 'default' : 'none',
          headerShadowVisible: false,
          ...navigationOptions,
        }}
      >
        <Stack.Screen
          name='agreement'
          options={{
            ...navigationOptions,
            headerShown: false,
            headerTitle: '',
            headerShadowVisible: false,

            // disagreed 说明已经进入首页
            // 使用 modal 展示，并且加上动画
            animation: agreementState === 'undetermined' ? 'none' : undefined,
            presentation:
              agreementState === 'undetermined' ? undefined : 'modal',
          }}
        />
        <Stack.Screen
          name='bookScanResult'
          options={{
            ...navigationOptions,
            headerShown: false,
            animation: 'none',
            presentation: 'containedTransparentModal',
          }}
        />
        <Stack.Screen
          name='(weChatOAuth)'
          options={{ ...navigationOptions, animation: 'none' }}
        />
        <Stack.Screen
          name='(tabs)'
          options={{
            ...navigationOptions,
            animation: 'none',
            headerShown: false,
            gestureEnabled: false,
          }}
        />
        <Stack.Screen
          name='(bindPhoneModal)'
          options={{
            ...navigationOptions,
            headerShown: false,
            presentation: 'modal',
          }}
        />
      </Stack>
      {/* <AIChatFloatBall
        renderInputToolBarTopNode={renderInputToolBarTopNode}
        aiBookInfo={aiBookInfo}
        onIntentCommandPayloadNeedBeHandled={handleAiBookIntentCommand}
      /> */}
      <LearnToolsModal
        visible={learnToolsModalVisible}
        onDismiss={onLearnToolsModalDismiss}
      />
    </NavigationThemeProvider>
  );
};

export default wrap(RootLayout);

export function ErrorBoundary({ error, retry }: ErrorBoundaryProps) {
  useEffect(() => {
    try {
      MemoryLogger.log(
        `App 崩溃 - ${typeof error === 'string' ? error : error.message}`,
      );
      captureEvent({
        message: `App 崩溃 - ${error.message}`,
        level: 'error',
        extra: {
          stack: error.stack,
        },
      });
    } catch (e) {
      MemoryLogger.log(`ErrorBoundary 捕获异常 - ${JSON.stringify(e)}`);
      console.error(e);
    }
  }, [error]);

  return (
    <View className='flex-1 items-center justify-center gap-y-4 bg-white'>
      <AlertTriangle size={48} color='#f97316' />

      <View className='items-center gap-y-2'>
        <Text style={{ fontSize: 20, fontWeight: '600', color: '#18181b' }}>
          页面加载失败
        </Text>
        <Text style={{ fontSize: 14, color: '#71717a', textAlign: 'center' }}>
          抱歉，页面遇到了一些问题。请尝试重新加载。
        </Text>
      </View>

      <Pressable
        className='bg-primary rounded-full p-4 text-white'
        onPress={retry}
      >
        <Text className='text-white'>重新加载</Text>
      </Pressable>
    </View>
  );
}
